#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于 requests + BeautifulSoup 的番号获取脚本
这是 get_codes.py 的改进版本，使用轻量级爬虫方式替代 Selenium
参考 get_magnet_sukebei.py 的爬虫实现方式
"""

import requests
from bs4 import BeautifulSoup
import re
import time
from datetime import datetime
from pathlib import Path
import sys
import os
from urllib.parse import urljoin
from collections import defaultdict
from tqdm import tqdm

# 添加父目录到路径，以便导入配置
sys.path.append(str(Path(__file__).parent.parent))
from av_config import *

class RequestsCodeScraper:
    """基于 requests + BeautifulSoup 的番号爬虫类"""
    
    def __init__(self):
        # 使用与 get_magnet_sukebei.py 相同的 headers 配置
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,ja;q=0.8,zh-CN;q=0.7',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 请求间隔配置（秒）
        self.request_delay = 2
        
    def is_date_in_range(self, release_date_str, date_range_filter_tuple):
        """检查日期是否在指定的日期范围内（复制自 get_codes.py）"""
        if not date_range_filter_tuple or (not date_range_filter_tuple[0] and not date_range_filter_tuple[1]):
            return True

        if not release_date_str:
            return False

        try:
            item_date = datetime.strptime(release_date_str, "%Y-%m-%d").date()
        except ValueError:
            print(f"警告：作品发行日期 '{release_date_str}' 格式无效，无法进行日期范围比较。将跳过此项。")
            return False

        start_date_str, end_date_str = date_range_filter_tuple
        
        start_date_obj = None
        if start_date_str:
            try:
                start_date_obj = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            except ValueError:
                print(f"警告：配置的开始日期 '{start_date_str}' 格式无效，此日期边界将被忽略。")

        end_date_obj = None
        if end_date_str:
            try:
                end_date_obj = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            except ValueError:
                print(f"警告：配置的结束日期 '{end_date_str}' 格式无效，此日期边界将被忽略。")

        if start_date_obj and item_date < start_date_obj:
            return False
        
        if end_date_obj and item_date > end_date_obj:
            return False
            
        return True
    
    def extract_code_info_from_item(self, item):
        """从作品项中提取番号信息"""
        try:
            # 查找番号
            code_text = ""

            # 方法1: 查找包含 fa-circle-o 的元素
            meta_items = item.find_all('li', class_=re.compile(r'.*fa-circle-o.*')) or item.find_all('li')

            for meta_item in meta_items:
                if 'fa-circle-o' in str(meta_item) or meta_item.find(class_='fa-circle-o'):
                    code_text = meta_item.get_text().strip()
                    if code_text:
                        break

            # 方法2: 如果方法1没找到，尝试其他方法
            if not code_text:
                possible_code_elements = item.find_all(['span', 'div', 'li'], string=re.compile(r'[A-Z]{2,5}[-_]?\d{2,5}', re.I))
                if possible_code_elements:
                    code_text = possible_code_elements[0].get_text().strip()

            if not code_text:
                return None

            # 查找发布日期
            release_date = ""
            date_elements = item.find_all(class_='fa-clock-o')
            if date_elements:
                date_parent = date_elements[0].parent
                if date_parent:
                    release_date = date_parent.get_text().strip()

            # 查找完整的作品标题
            full_title = self.extract_full_title(item)

            # 查找视频类型和VR标识
            is_vr = False
            video_type = full_title if full_title else "未知类型"

            # 检查是否为VR作品
            if full_title and "VR" in full_title.upper():
                is_vr = True

            # 如果没有找到完整标题，尝试从分类链接获取
            if not full_title:
                categories = item.find_all('a')
                for category in categories:
                    category_text = category.get_text()
                    if "VR" in category_text:
                        is_vr = True
                    if category_text.strip() and video_type == "未知类型":
                        video_type = category_text.strip()

            # 查找演员数量
            actress_count = 0
            actress_elements = item.find_all(class_='actress-name') or item.find_all('a', href=re.compile(r'actress'))
            actress_count = len(actress_elements)

            # 如果没有找到演员元素，默认为1（单体）
            if actress_count == 0:
                actress_count = 1

            return code_text, release_date, video_type, is_vr, actress_count

        except Exception as e:
            print(f"提取番号信息时出错: {e}")
            return None

    def extract_full_title(self, item):
        """提取完整的作品标题，优先从title属性获取"""
        try:
            # 方法1: 查找带有title属性的链接元素（最优先）
            # 根据调试结果，完整标题存储在链接的title属性中
            title_links = item.find_all('a', title=True)
            for link in title_links:
                title_attr = link.get('title', '').strip()
                # 检查是否是作品标题（长度合理且不是简单的导航文本）
                if (title_attr and
                    len(title_attr) > 15 and  # 作品标题通常较长
                    not title_attr in ['続きを読む', 'FANZA', '詳細を見る'] and  # 排除导航文本
                    not re.match(r'^[A-Z]{2,5}[-_]?\d{2,5}$', title_attr)):  # 不是番号
                    return title_attr

            # 方法2: 查找img元素的alt属性（备选方案）
            # 从调试结果看，img的alt属性也包含完整标题
            img_elements = item.find_all('img', alt=True)
            for img in img_elements:
                alt_text = img.get('alt', '').strip()
                if (alt_text and
                    len(alt_text) > 15 and
                    not re.match(r'^[A-Z]{2,5}[-_]?\d{2,5}$', alt_text)):
                    return alt_text

            # 方法3: 查找h2标题元素中的链接title属性
            # 基于HTML结构分析，标题在 h2.archive-header-title > a 中
            header_titles = item.select('h2.archive-header-title a[title]')
            for title_link in header_titles:
                title_attr = title_link.get('title', '').strip()
                if title_attr and len(title_attr) > 15:
                    return title_attr

            # 方法4: 查找archive-header-title类中的链接
            archive_header_links = item.select('.archive-header-title a')
            for link in archive_header_links:
                title_attr = link.get('title', '').strip()
                if title_attr and len(title_attr) > 15:
                    return title_attr

            # 方法5: 如果以上都没找到，查找所有链接中最长的title属性
            all_links = item.find_all('a')
            longest_title = ""

            for link in all_links:
                title_attr = link.get('title', '').strip()
                if (len(title_attr) > len(longest_title) and
                    len(title_attr) > 15 and
                    not title_attr in ['続きを読む', 'FANZA', '詳細を見る']):
                    longest_title = title_attr

            if longest_title:
                return longest_title

            # 方法6: 最后尝试从链接文本获取（可能被截断）
            for link in all_links:
                link_text = link.get_text().strip()
                if (link_text and
                    len(link_text) > 20 and
                    not re.match(r'^[A-Z]{2,5}[-_]?\d{2,5}$', link_text) and
                    not link_text in ['続きを読む', 'FANZA', '詳細を見る']):
                    return link_text

            return None

        except Exception as e:
            print(f"提取完整标题时出错: {e}")
            return None
    
    def scrape_page(self, url):
        """爬取单个页面的番号信息"""
        try:
            print(f"正在访问: {url}")
            response = self.session.get(url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找作品列表项
            archive_items = soup.find_all(class_="archive-list")
            
            if not archive_items:
                print("未找到 archive-list 元素")
                return None, None
                
            print(f"找到 {len(archive_items)} 个作品项")
            
            page_results = {
                'normal': [],
                'vr': [],
                'multi_actor': [],
                'collaborative': [],
                'collection': []
            }
            
            for item in archive_items:
                try:
                    code_info = self.extract_code_info_from_item(item)
                    if code_info:
                        code_text, release_date, video_type, is_vr, actress_count = code_info
                        
                        # 应用日期过滤器
                        if not self.is_date_in_range(release_date, DATE_RANGE_FILTER):
                            continue
                        
                        if is_vr:
                            page_results['vr'].append((code_text, release_date, video_type))
                        else:
                            # 判断作品类型
                            if actress_count == 1:
                                video_type = f"{video_type} (单体)"
                                page_results['normal'].append((code_text, release_date, video_type))
                            elif actress_count > 1:
                                if actress_count <= MULTI_ACTOR_THRESHOLD:
                                    collaborative_type = f"{video_type} (共演-{actress_count}人)"
                                    page_results['collaborative'].append((code_text, release_date, collaborative_type))
                                else:
                                    collection_type = f"{video_type} (合集-{actress_count}人)"
                                    page_results['collection'].append((code_text, release_date, collection_type))
                                
                                page_results['multi_actor'].append((code_text, release_date, f"{video_type} (多人-{actress_count}人)"))
                            else:
                                page_results['normal'].append((code_text, release_date, video_type))
                                
                except Exception as e:
                    print(f"处理作品项时出错: {e}")
                    continue
            
            # 查找下一页URL
            next_url = None
            try:
                next_page_element = soup.find('a', class_='next page-numbers')
                if next_page_element:
                    next_url = next_page_element.get('href')
                    if next_url:
                        next_url = urljoin(url, next_url)
            except Exception as e:
                print(f"查找下一页时出错: {e}")
            
            return page_results, next_url
            
        except Exception as e:
            print(f"爬取页面时出错: {e}")
            return None, None
    
    def scrape_all_pages(self, start_url, max_pages=None):
        """爬取所有页面的番号信息"""
        print(f"开始爬取番号，起始URL: {start_url}")
        
        all_codes_info = {
            'normal': [],
            'vr': [],
            'multi_actor': [],
            'collaborative': [],
            'collection': []
        }
        
        current_url = start_url
        current_page = 1
        
        # 使用进度条
        pbar = tqdm(desc="爬取进度", unit="页")
        
        try:
            while current_url:
                if max_pages and current_page > max_pages:
                    print(f"已达到最大页数限制: {max_pages}")
                    break
                
                pbar.set_description(f"处理第 {current_page} 页")
                
                page_results, next_url = self.scrape_page(current_url)
                
                if page_results:
                    # 合并结果
                    for code_type, codes_list in page_results.items():
                        all_codes_info[code_type].extend(codes_list)
                    
                    # 更新进度条信息
                    total_codes = sum(len(codes) for codes in all_codes_info.values())
                    pbar.set_postfix({
                        "总计": total_codes,
                        "普通": len(all_codes_info['normal']),
                        "VR": len(all_codes_info['vr'])
                    })
                
                current_url = next_url
                current_page += 1
                pbar.update(1)
                
                # 添加请求间隔
                if current_url:
                    time.sleep(self.request_delay)
                    
        finally:
            pbar.close()
        
        return all_codes_info

def main():
    """主函数"""
    print("✨ 基于 requests + BeautifulSoup 的番号获取工具")
    print("=" * 60)
    
    # 从配置文件获取必要的配置项
    actress_url = ACTRESS_URL
    file_paths = get_file_paths()
    unified_output_file = file_paths["unified_codes_file"]
    
    print(f"演员页面URL: {actress_url}")
    print(f"统一番号输出文件: {unified_output_file}")
    print(f"作品分类规则: 1人=单体，2-{MULTI_ACTOR_THRESHOLD}人=共演，>{MULTI_ACTOR_THRESHOLD}人=合集")
    print(f"日期过滤范围: {DATE_RANGE_FILTER}")
    
    # 创建爬虫实例
    scraper = RequestsCodeScraper()
    
    try:
        # 爬取所有页面
        all_codes_info = scraper.scrape_all_pages(actress_url)
        
        # 按发布日期对番号进行排序（新的在前）
        for code_type in all_codes_info:
            all_codes_info[code_type] = sorted(
                all_codes_info[code_type],
                key=lambda x: x[1] if x[1] else "0000-00-00",
                reverse=True
            )
        
        # 保存所有番号到统一文件
        with open(unified_output_file, 'w', encoding='utf-8') as f:
            for code_type in ['normal', 'vr', 'collaborative', 'collection']:
                if code_type in ENABLED_CODE_TYPES and all_codes_info[code_type]:
                    f.write(f"# {AVAILABLE_CODE_TYPES[code_type]}\n")
                    for code, date, video_type in all_codes_info[code_type]:
                        f.write(f"{code} ({date}) [{video_type}]\n")
                    f.write("\n")
        
        # 输出统计信息
        print("\n" + "=" * 60)
        print("爬取完成！统计信息:")
        print("=" * 60)
        
        total_codes = sum(len(codes) for codes in all_codes_info.values())
        print(f"总计获取番号: {total_codes} 个")
        
        for code_type, codes_list in all_codes_info.items():
            if codes_list:
                print(f"  - {AVAILABLE_CODE_TYPES.get(code_type, code_type)}: {len(codes_list)} 个")
        
        print(f"\n已保存到文件: {unified_output_file}")
        
    except Exception as e:
        print(f"程序发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
